import type { SVGProps } from "react";
const SvgAutomation = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <g
            stroke="currentColor"
            strokeWidth={1.14}
            clipPath="url(#automation_svg__a)"
        >
            <path d="M10.4 4.345h2.4c.31 0 .55.273.55.6v6.11c0 .338-.25.6-.55.6h-2.4m-4.8-7.31H3.2c-.31 0-.56.262-.56.6v6.11c0 .327.25.6.56.6h2.4" />
            <path d="M5.14 3.31c0-.339.25-.612.55-.612h4.6c.31 0 .55.273.55.611v1.746c0 .338-.25.6-.55.6H5.7c-.31 0-.55-.273-.55-.6V3.309zM5.14 10.945c0-.338.25-.6.55-.6h4.6c.31 0 .55.273.55.6v1.746c0 .338-.25.6-.55.6H5.7c-.31 0-.55-.273-.55-.6v-1.746z" />
        </g>
        <defs>
            <clipPath id="automation_svg__a">
                <path fill="#fff" d="M2 2h12v12H2z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgAutomation;
