import type { SVGProps } from "react";
const SvgEvaluateSettings = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M13.875 2C14.496 2 15 2.524 15 3.17V6.5h-1.25V3.755a.447.447 0 0 0-.437-.455H2.687c-.239 0-.433.2-.437.447v7.498c0 .*************.455h3.851V13H2.125C1.504 13 1 12.476 1 11.83V3.17C1 2.524 1.504 2 2.125 2zM4.719 4.77v1.3H3.344v-1.3zm3 0v1.3H5.344v-1.3z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            d="m10.564 13.081-2.319-.075-.385-2.284L12.155 5.8l2.704 2.36zm-1.376-2.037.124.734.747.025 3.019-3.46-.87-.76z"
        />
    </svg>
);
export default SvgEvaluateSettings;
