import type { SVGProps } from "react";
const SvgProjectSettings = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M13.875 2C14.496 2 15 2.524 15 3.17V7.2h-1.25V3.756a.447.447 0 0 0-.437-.455H2.687c-.239 0-.433.2-.437.447v7.498c0 .*************.455h4.211V13H2.125C1.504 13 1 12.476 1 11.83V3.17C1 2.524 1.504 2 2.125 2zM4.719 4.77v1.3H3.344v-1.3zm3 0v1.3H5.344v-1.3z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M8.917 9.954c-.1.406-.1.83 0 1.236l-.774.436.78 1.32.773-.437c.31.287.686.5 1.096.619V14h1.56v-.872c.41-.12.784-.332 1.095-.619l.774.437.78-1.32-.774-.436c.098-.407.098-.83 0-1.236L15 9.517l-.78-1.32-.773.437a2.74 2.74 0 0 0-1.096-.619v-.872h-1.56v.872c-.409.12-.784.332-1.095.62l-.773-.437-.78 1.32zm3.824.618c0 .63-.523 1.143-1.17 1.143-.645 0-1.169-.512-1.169-1.143 0-.632.524-1.143 1.17-1.143s1.17.511 1.17 1.143"
            clipRule="evenodd"
        />
    </svg>
);
export default SvgProjectSettings;
