import type { SVGProps } from "react";
const SvgReflow = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            d="M3.625 3.331h2.009a.7.7 0 0 1 .485.195l.072.081 1.82 2.4a.7.7 0 0 1-1.045.924l-.069-.078L5.285 4.73h-1.66v1.7L1 4.215 3.625 2zM10.56 6.911a.7.7 0 0 1 .096 1.394l-.095.006h-.966l-2.955 3.09a.7.7 0 0 1-.393.208l-.113.009H3.068a.7.7 0 0 1-.095-1.394l.095-.006h2.767l2.957-3.09a.7.7 0 0 1 .393-.208l.113-.009z"
        />
        <path
            fill="currentColor"
            d="M3.625 8.711v4.43L1 10.926zM12.068 5.611a2 2 0 1 1 0 4 2 2 0 0 1 0-4m0 1.212a.788.788 0 1 0 0 1.577.788.788 0 0 0 0-1.577"
        />
    </svg>
);
export default SvgReflow;
