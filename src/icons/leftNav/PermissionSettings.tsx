import type { SVGProps } from "react";
const SvgPermissionSettings = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M13.875 2C14.496 2 15 2.524 15 3.17V6.5h-1.25V3.755a.447.447 0 0 0-.437-.455H2.687c-.239 0-.433.2-.437.447v7.498c0 .*************.455h3.851V13H2.125C1.504 13 1 12.476 1 11.83V3.17C1 2.524 1.504 2 2.125 2zM4.719 4.77v1.3H3.344v-1.3zm3 0v1.3H5.344v-1.3z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            d="M14.003 7.192a2.322 2.322 0 1 1-3.284 3.285 2.322 2.322 0 0 1 3.284-3.285m-.966.966a.956.956 0 1 0-1.352 1.352.956.956 0 0 0 1.352-1.352"
        />
        <path
            fill="currentColor"
            d="m11.704 10.496-2.901 2.9-.966-.965 2.9-2.901z"
        />
        <path
            fill="currentColor"
            d="m10.187 11.062 1.387 1.51-1.006.925-1.388-1.51z"
        />
    </svg>
);
export default SvgPermissionSettings;
