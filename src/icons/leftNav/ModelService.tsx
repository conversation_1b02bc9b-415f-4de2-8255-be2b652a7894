import type { SVGProps } from "react";
const SvgModelService = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            stroke="currentColor"
            strokeWidth={1.21}
            d="M13.04 8.605c.416 0 .748.144.974.369.224.223.38.563.38 1.026 0 .34-.147.745-.407 1.163-.254.41-.583.777-.863 1.031l-.014.013-.012.013c-.31.316-.838.657-1.598 1.085-.76-.428-1.288-.77-1.598-1.085l-.011-.012-.012-.01-.24-.234a5 5 0 0 1-.65-.825c-.247-.401-.384-.79-.384-1.139 0-.463.157-.803.381-1.026.226-.225.558-.369.974-.369.41 0 .742.171 1.077.57l.463.552.463-.552c.335-.399.668-.57 1.077-.57Z"
        />
        <path
            stroke="currentColor"
            strokeWidth={1.2}
            d="M2 2.6h2c.22 0 .4.18.4.4v10a.4.4 0 0 1-.4.4H2a.4.4 0 0 1-.4-.4V3c0-.22.18-.4.4-.4Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M12.237 2c.621 0 1.125.524 1.125 1.17V7h-1.25V3.755a.446.446 0 0 0-.437-.455H7.687c-.239 0-.433.2-.437.447v8.498c0 .*************.455h.712V14H7.125C6.504 14 6 13.476 6 12.83V3.17C6 2.524 6.504 2 7.125 2z"
            clipRule="evenodd"
        />
    </svg>
);
export default SvgModelService;
