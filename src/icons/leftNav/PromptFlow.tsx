import type { SVGProps } from "react";
const SvgPromptFlow = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            d="M4.6 10.5a.4.4 0 0 0 .319.392L5 10.9h4V10a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-.9H5a1.6 1.6 0 0 1-1.594-1.454L3.4 10.5v-4h-.9a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-.9zm5.6 2.3h2.6v-2.6h-2.6zM2.7 5.3h2.6V2.7H2.7z"
        />
    </svg>
);
export default SvgPromptFlow;
