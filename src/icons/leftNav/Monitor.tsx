import type { SVGProps } from "react";
const SvgMonitor = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            d="M12.5 8a2.5 2.5 0 0 1 2.5 2.5v2.204h.532V14h-6.08v-1.296H10V10.5A2.5 2.5 0 0 1 12.5 8m0 1.1a1.4 1.4 0 0 0-1.394 1.265l-.006.135v2.204h2.8V10.5a1.4 1.4 0 0 0-1.265-1.394z"
        />
        <path
            fill="currentColor"
            d="m12.9 9.5-.189 1.277h.789L12.1 12.5l.189-1.277H11.5z"
        />
        <path
            stroke="currentColor"
            strokeWidth={1.2}
            d="M2.3 2.6h2c.22 0 .4.18.4.4v10a.4.4 0 0 1-.4.4h-2a.4.4 0 0 1-.4-.4V3c0-.22.179-.4.4-.4Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M12.537 2c.621 0 1.125.524 1.125 1.17V7h-1.25V3.755a.446.446 0 0 0-.438-.455H7.987c-.239 0-.433.2-.437.447v8.498c0 .*************.455h.712V14H7.425c-.622 0-1.125-.524-1.125-1.17V3.17C6.3 2.524 6.803 2 7.425 2z"
            clipRule="evenodd"
        />
    </svg>
);
export default SvgMonitor;
