import type { SVGProps } from "react";
const SvgInstrumentalization = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <g
            stroke="currentColor"
            strokeWidth={1.14}
            clipPath="url(#instrumentalization_svg__a)"
        >
            <path d="m10.45 8 2.6 2.6c.***********.1.28l-.12 1.35c-.01.15-.12.28-.27.32l-1.79.42c-.11.03-.24 0-.32-.09L8 10.23M5.77 7.95 2.86 5.09a.35.35 0 0 1 0-.5L4.7 2.75a.35.35 0 0 1 .5 0L8 5.5" />
            <path d="M10.49 2.93a.35.35 0 0 1 .5 0l2.23 2.23c.*********** 0 .5L5.48 13.4a.35.35 0 0 1-.5 0l-2.23-2.23a.35.35 0 0 1 0-.5zM5.62 8.51l1 1M7.12 6.51l1.5 1.5M9.12 5.01l1 1" />
        </g>
        <defs>
            <clipPath id="instrumentalization_svg__a">
                <path fill="#fff" d="M2 2h11.96v12.15H2z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgInstrumentalization;
