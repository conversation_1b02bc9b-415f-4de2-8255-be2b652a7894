import type { SVGProps } from "react";
const SvgQianfan = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path fill="#fff" fillOpacity={0.01} d="M1 1h14v14H1z" />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M8.355 4.198a.73.73 0 0 0-.708 0L4.79 5.786a.73.73 0 0 0-.375.636v3.163c0 .265.143.509.375.637l2.856 1.59a.73.73 0 0 0 .708 0l2.858-1.589a.73.73 0 0 0 .376-.638V6.422a.73.73 0 0 0-.376-.636zM5.463 6.611 8 5.201l2.538 1.411v2.784l-2.539 1.41-2.536-1.41zM4.15 1.875a.525.525 0 0 0-.525-.525h-1.75a.525.525 0 0 0-.525.525v1.75c0 .29.235.525.525.525h1.75a.525.525 0 0 0 .525-.525zM2.4 2.4h.7v.7h-.7zm1.75 9.975v1.75a.525.525 0 0 1-.525.525h-1.75a.525.525 0 0 1-.525-.525v-1.75c0-.29.235-.525.525-.525h1.75c.29 0 .525.235.525.525M2.4 12.9h.7v.7h-.7zM14.125 1.35c.29 0 .525.235.525.525v1.75c0 .29-.235.525-.525.525h-1.75a.525.525 0 0 1-.525-.525v-1.75c0-.29.235-.525.525-.525zM12.9 2.4h.7v.7h-.7zm1.225 9.45c.29 0 .525.235.525.525v1.75c0 .29-.235.525-.525.525h-1.75a.525.525 0 0 1-.525-.525v-1.75c0-.29.235-.525.525-.525zm-3.5-9.625a.525.525 0 0 1 0 1.05h-5.25a.525.525 0 1 1 0-1.05zm.525 11.025a.525.525 0 0 0-.525-.525h-5.25a.525.525 0 1 0 0 1.05h5.25c.29 0 .525-.235.525-.525m1.75-.35h.7v.7h-.7zm-9.625-2.275a.525.525 0 0 1-1.05 0v-5.25a.525.525 0 1 1 1.05 0zm9.975.525c.29 0 .525-.235.525-.525v-5.25a.525.525 0 0 0-1.05 0v5.25c0 .29.235.525.525.525"
            clipRule="evenodd"
        />
    </svg>
);
export default SvgQianfan;
