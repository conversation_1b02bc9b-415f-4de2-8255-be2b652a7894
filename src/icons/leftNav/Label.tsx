import type { SVGProps } from "react";
const SvgLabel = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path fill="currentColor" d="M1 1h14v14H1z" opacity={0.01} />
        <path
            fill="currentColor"
            d="M12.398 2.531c.591 0 1.07.44 1.07.984v8.97c0 .543-.479.984-1.07.984h-8.38c-.821 0-1.487-.611-1.487-1.365v-8.59c0-.542.48-.983 1.07-.983zM4.137 3.624c-.23 0-.416.17-.416.382v8.098c0 .149.13.27.292.272h7.85c.23 0 .416-.17.416-.382V4.006c0-.211-.186-.382-.416-.382z"
        />
        <path
            stroke="currentColor"
            strokeWidth={1.2}
            d="M3.55 7.05h5c.22 0 .4.18.4.4v5a.4.4 0 0 1-.4.4h-5a.4.4 0 0 1-.4-.4v-5c0-.22.179-.4.4-.4Z"
        />
    </svg>
);
export default SvgLabel;
