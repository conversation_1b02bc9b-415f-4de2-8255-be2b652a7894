import type { SVGProps } from "react";
const SvgEvaluateReport = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M13 2.5a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-10a1 1 0 0 1 1-1h2v1.1H3.1v9.8h9.8V3.6H11V2.5z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M11 2.5a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1zm-1 0H6v1h4zM10.297 8.21c0-1.497-1.186-2.71-2.649-2.71C6.186 5.5 5 6.713 5 8.21s1.186 2.71 2.648 2.71c.548 0 1.057-.17 1.48-.462l1.018 1.042.854-.873-1.05-1.075c.22-.396.347-.854.347-1.342m-4.09 0c0-.815.646-1.475 1.441-1.475.796 0 1.442.66 1.442 1.475 0 .814-.646 1.474-1.442 1.474s-1.44-.66-1.44-1.474"
            clipRule="evenodd"
        />
    </svg>
);
export default SvgEvaluateReport;
