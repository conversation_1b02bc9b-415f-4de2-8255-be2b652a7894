import type { SVGProps } from "react";
const SvgEvaluateTask = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path fill="currentColor" d="M1 1h14v14H1z" opacity={0.01} />
        <path
            fill="currentColor"
            d="M13.715 3.28v5.304c0 .994-.507 1.958-1.466 2.893-.89.867-2.194 1.735-3.914 2.613l-.337.172-.334-.178c-1.696-.9-2.987-1.777-3.875-2.642-.955-.929-1.473-1.871-1.504-2.835v-5.33l5.741-1.785zm-10.29.838v4.46l.01.126c.064.576.422 1.206 1.127 1.897l.022.022q1.135 1.107 3.378 2.325l.047.026.002-.001c1.52-.79 2.657-1.555 3.419-2.29l.023-.024c.77-.75 1.121-1.437 1.121-2.075V4.117L8.025 2.688z"
        />
        <path
            fill="currentColor"
            d="M7.954 4.638a2.504 2.504 0 0 1 2.173 3.745l.994.993-.807.807-.963-.963a2.504 2.504 0 1 1-1.397-4.582m0 1.141a1.363 1.363 0 0 0 0 2.726 1.362 1.362 0 0 0 0-2.726"
        />
    </svg>
);
export default SvgEvaluateTask;
