import type { SVGProps } from "react";
const SvgModelManage = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M9.677 10.46a2.3 2.3 0 0 0 0 1.081L9 11.923l.682 1.154.677-.382c.272.252.6.437.959.542V14h1.364v-.763a2.4 2.4 0 0 0 .959-.542l.677.382.682-1.154-.677-.382a2.3 2.3 0 0 0 0-1.082l.677-.382-.682-1.154-.677.382a2.4 2.4 0 0 0-.959-.542V8h-1.364v.763a2.4 2.4 0 0 0-.959.542l-.677-.382L9 10.077zm3.346.54c0 .552-.458 1-1.023 1a1.01 1.01 0 0 1-1.023-1c0-.552.458-1 1.023-1s1.023.448 1.023 1"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            d="M4 2a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zM2.2 12.8h1.6V3.2H2.2z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M12.237 2c.621 0 1.125.524 1.125 1.17V7h-1.25V3.755a.446.446 0 0 0-.437-.455H7.687c-.239 0-.433.2-.437.447v8.498c0 .*************.455h.712V14H7.125C6.504 14 6 13.476 6 12.83V3.17C6 2.524 6.504 2 7.125 2z"
            clipRule="evenodd"
        />
    </svg>
);
export default SvgModelManage;
