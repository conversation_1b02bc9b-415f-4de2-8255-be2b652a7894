import type { SVGProps } from "react";
const SvgPromptManage = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="currentColor"
            d="M13 3a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zM4.2 7.8h6.7a.6.6 0 0 1 0 1.2H4.2v3.8h8.6V4.2H4.2zm6.7 2.7a.6.6 0 0 1 0 1.2H8.1a.6.6 0 0 1 0-1.2zm0-5a.6.6 0 0 1 0 1.2H8.1a.6.6 0 0 1 0-1.2z"
        />
    </svg>
);
export default SvgPromptManage;
